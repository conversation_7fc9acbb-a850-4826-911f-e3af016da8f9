<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plans;

class DefaultPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if plans already exist
        if (Plans::count() > 0) {
            $this->command->info('Plans already exist!');
            return;
        }

        // Create default plans
        $plans = [
            [
                'name' => 'Free Trial',
                'price' => 0,
                'currency' => 'USD',
                'duration' => 7,
                'duration_type' => 'days',
                'features' => json_encode([
                    'devices' => 1,
                    'messages_per_day' => 50,
                    'ai_bot' => false,
                    'campaigns' => false,
                    'auto_reply' => true,
                    'webhook' => true
                ]),
                'description' => 'Free trial plan with basic features',
                'is_active' => true,
                'is_trial' => true
            ],
            [
                'name' => 'Basic Plan',
                'price' => 9.99,
                'currency' => 'USD',
                'duration' => 1,
                'duration_type' => 'month',
                'features' => json_encode([
                    'devices' => 3,
                    'messages_per_day' => 1000,
                    'ai_bot' => true,
                    'campaigns' => true,
                    'auto_reply' => true,
                    'webhook' => true,
                    'blast_messages' => true
                ]),
                'description' => 'Basic plan for small businesses',
                'is_active' => true,
                'is_trial' => false
            ],
            [
                'name' => 'Professional Plan',
                'price' => 29.99,
                'currency' => 'USD',
                'duration' => 1,
                'duration_type' => 'month',
                'features' => json_encode([
                    'devices' => 10,
                    'messages_per_day' => 5000,
                    'ai_bot' => true,
                    'campaigns' => true,
                    'auto_reply' => true,
                    'webhook' => true,
                    'blast_messages' => true,
                    'advanced_analytics' => true,
                    'priority_support' => true
                ]),
                'description' => 'Professional plan for growing businesses',
                'is_active' => true,
                'is_trial' => false
            ],
            [
                'name' => 'Enterprise Plan',
                'price' => 99.99,
                'currency' => 'USD',
                'duration' => 1,
                'duration_type' => 'month',
                'features' => json_encode([
                    'devices' => -1, // unlimited
                    'messages_per_day' => -1, // unlimited
                    'ai_bot' => true,
                    'campaigns' => true,
                    'auto_reply' => true,
                    'webhook' => true,
                    'blast_messages' => true,
                    'advanced_analytics' => true,
                    'priority_support' => true,
                    'custom_integrations' => true,
                    'white_label' => true
                ]),
                'description' => 'Enterprise plan with unlimited features',
                'is_active' => true,
                'is_trial' => false
            ]
        ];

        foreach ($plans as $plan) {
            Plans::create($plan);
        }

        $this->command->info('Default plans created successfully!');
        $this->command->info('Created ' . count($plans) . ' plans');
    }
}
