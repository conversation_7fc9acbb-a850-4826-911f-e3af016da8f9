name: run-tests

on:
    push:
        branches: [master]
    pull_request:
        branches: [master]

jobs:
    test:
        runs-on: ${{ matrix.os }}
        strategy:
            fail-fast: true
            matrix:
                os: [ubuntu-latest]
                php: [8.4, 8.3, 8.2]
                laravel: [12.*, 11.*, 10.*]
                stability: [prefer-lowest, prefer-stable]
                include:
                  - laravel: 12.*
                    testbench: 10.*
                  - laravel: 11.*
                    testbench: 9.*
                  - laravel: 10.*
                    testbench: 8.*

        name: P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.stability }} - ${{ matrix.os }}

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}
                  extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, fileinfo
                  coverage: none

            - name: Setup problem matchers
              run: |
                  echo "::add-matcher::${{ runner.tool_cache }}/php.json"
                  echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

            - name: Install dependencies
              run: |
                  composer require "laravel/framework:${{ matrix.laravel }}" "orchestra/testbench-browser-kit:${{ matrix.testbench }}" --no-interaction --no-update
                  composer update --${{ matrix.stability }} --prefer-dist --no-interaction

            - name: Execute tests
              run: vendor/bin/phpunit