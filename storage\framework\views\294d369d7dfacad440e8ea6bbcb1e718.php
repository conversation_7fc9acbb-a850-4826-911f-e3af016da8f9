                               <li class="menu-item">
							   <div class="menu-link px-0">
                                   <select class="form-control" id="device_idd" name="device_id" style="cursor: pointer;">
                                       <option value="" disabled selected><?php echo e(__('Select Device')); ?></option>
                                       <?php $__currentLoopData = $numbers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                           
                                           <?php if(Session::has('selectedDevice') && Session::get('selectedDevice')['device_body'] == $device->body): ?>
                                               
                                               <option value="<?php echo e($device->id); ?>" selected><?php echo e($device->body); ?>

                                                   (<?php echo e(__($device->status)); ?>)</option>
                                           <?php else: ?>
                                               <option value="<?php echo e($device->id); ?>"><?php echo e($device->body); ?>

                                                   (<?php echo e(__($device->status)); ?>)</option>
                                           <?php endif; ?>
                                       <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                   </select>
								   </div>
                               </li>

                               <script>
                                   //  on select device
                                window.onload = function() {
									$(document).ready(function() {
									   $('#device_idd').on('change', function() {
										   var device = $(this).val();
										   $.ajax({
											   url: "<?php echo e(route('setSessionSelectedDevice')); ?>",
											   type: "POST",
											   data: {
												   _token: "<?php echo e(csrf_token()); ?>",
												   device: device
											   },
											   success: function(data) { // reload page
												   if (data.error) {
													   notyf.error(data.msg);
													   // reload in 1 second
													   setTimeout(function() {
														   location.reload();
													   }, 1000);
												   } else {
													   notyf.success(data.msg);
													   // reload in 1 second
													   setTimeout(function() {
														   location.reload();
													   }, 1000);
												   }
											   }
										   });
									   });
									});
								};
                               </script>
<?php /**PATH C:\xampp\htdocs\mp\resources\themes/vuexy/views/components/select-device.blade.php ENDPATH**/ ?>