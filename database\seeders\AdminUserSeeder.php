<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if admin user already exists
        if (User::where('email', '<EMAIL>')->exists()) {
            $this->command->info('Admin user already exists!');
            return;
        }

        // Create admin user
        $user = User::create([
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'api_key' => Str::random(32),
            'level' => 'admin',
            'status' => 'active',
            'limit_device' => 10,
            'active_subscription' => 1,
            'subscription_expired' => now()->addYear(),
            'timezone' => 'UTC',
            'chunk_blast' => 100,
            'two_factor_enabled' => false,
            'delete_history' => 30,
            'plan_name' => 'Admin Plan',
            'plan_data' => json_encode([
                'features' => ['unlimited_devices', 'unlimited_messages', 'ai_bot', 'campaigns'],
                'limits' => ['devices' => -1, 'messages' => -1]
            ])
        ]);

        $this->command->info('Admin user created successfully!');
        $this->command->info('Username: admin');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: admin123');
        $this->command->info('API Key: ' . $user->api_key);
    }
}
