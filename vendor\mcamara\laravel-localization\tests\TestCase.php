<?php

namespace <PERSON><PERSON><PERSON>\LaravelLocalization\Tests;

use <PERSON><PERSON><PERSON>\LaravelLocalization\Facades\LaravelLocalization;
use <PERSON>camara\LaravelLocalization\LaravelLocalizationServiceProvider;
use Orchestra\Testbench\BrowserKit\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    protected function getPackageProviders($app)
    {
        return [LaravelLocalizationServiceProvider::class];
    }

    protected function getPackageAliases($app)
    {
        return [
            'LaravelLocalization' => LaravelLocalization::class,
        ];
    }
}
