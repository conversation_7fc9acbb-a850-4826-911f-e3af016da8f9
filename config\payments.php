<?php return array (
  'midtrans' => 
  array (
    'status' => 'disable',
    'merchant_id' => 'G12xxxxxx',
    'server_key' => 'SB-Mid-server-xxxxxx',
    'client_key' => 'SB-Mid-client-xxxxxx',
    'is_production' => 'false',
  ),
  'stripe' => 
  array (
    'status' => 'disable',
    'secret_key' => 'sk_xxxxxx',
    'publishable_key' => 'pk_xxxxxx',
    'webhook_secret' => 'whsec_xxxxxx',
  ),
  'paypal' => 
  array (
    'status' => 'disable',
    'client_id' => 'AYmthwTq8grg1gPJGtenCX_jVuJTfCiAU6T1xd0AJl1mlatjcEkEUVoujaWdveqqSVfqqMlsolg-JM3O',
    'client_secret' => 'EIjkvaeoVkPmIgChM1z3SCvF6JkkpRYD7ghZujhtM_p__3s6UGHleWgP0CwRoSFCTJkR-blNK38mC7so',
    'mode' => 'sandbox',
  ),
  'paymob' => 
  array (
    'status' => 'disable',
    'hmac_key' => 'C49A3DDAxxxxxx',
    'integration_id' => '489xxxxx',
    'secret_key' => 'egy_sk_xxxxxx',
    'public_key' => 'egy_pk_xxxxxx',
  ),
  'nowpayments' => 
  array (
    'status' => 'disable',
    'api_key' => 'K8Q3MD1-0RKMQE9-H8FZZ5K-SH384NX',
    'public_key' => 'd821d0ac-1826-4869-ba85-426a6d515040',
    'pay_currency' => 'BTC',
  ),
  'paystack' => 
  array (
    'status' => 'disable',
    'secret_key' => 'sk_test_0367dc91ce0f042859bc0bd3a93be18e0d2dfb12',
    'public_key' => 'pk_test_c8c6402a7b79abfc693bbdbaa0afeca247e0802b',
  ),
  'flutterwave' => 
  array (
    'status' => 'disable',
    'public_key' => 'FLWPUBK_TEST-8bb055c47c3bbca883ddf70a20c84556-X',
    'secret_key' => 'FLWSECK_TEST-53674d9b3c873411bc2874dc26a5d38f-X',
    'encryption_key' => 'FLWSECK_TEST2fb2b04c90ef',
    'payment_options' => 'card,banktransfer,ussd,credit,opay,account,internetbanking,googlepay,enaira',
  ),
  'duitku' => 
  array (
    'status' => 'disable',
    'api_key' => '9e2ac65a05df36bb34cee9f674b26532',
    'merchant_code' => 'DS21395',
    'paymentMethod' => 'VC',
    'is_production' => 'false',
  ),
  'tripay' => 
  array (
    'status' => 'disable',
    'api_key' => 'DEV-2LVXLmt8UbAMtdGr20b7IUcOwaFB1TPCT7MqM83Y',
    'private_key' => 'NVsmc-tIVrZ-j724L-Pex6A-fBbim',
    'merchant_code' => 'T19855',
    'is_production' => 'false',
  ),
  'xendit' => 
  array (
    'status' => 'disable',
    'secret_key' => 'xnd_development_EAhu524MkVsLLXpLLaHH6eakOjXLqZ1ssrUOw9XaskRfgIHhRMElR2dnQAoq6R',
    'public_key' => 'xnd_public_development_PtoPdKiKvseWrXgLshTHtVUlOpIgYf3LA1DZkDWF3a_GOzcldMLXQduNFSSdVeyj',
  ),
  'geniebiz' => 
  array (
    'status' => 'disable',
    'application_id' => '1c4e3e32-b2f3-4c6b-aa98-b045d9afd94d',
    'api_key' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************.F4Na4gJuuqnMMxDkplpv9fknnSTA9SKVVBcppNipGmg',
    'is_production' => 'true',
  ),
  'custom' => 
  array (
    'status' => 'disable',
    'title' => 'Bank transfer',
    'html' => '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  ),
  'fawaterk' =>
  array (
    'status'       => 'enable',
    'api_key'      => '0f95327f0c972c4a68fa902fded6ddc6902563b1c24eb4196e',
    'provider_key' => 'FAWATERAK.1432',
	'mode' => 'sandbox',
  ),
);