{"name": "@cacheable/node-cache", "version": "1.5.8", "description": "Simple and Maintained fast NodeJS internal caching", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/node-cache"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "keywords": ["cache", "caching", "node", "nodejs", "cacheable", "cacheable-node-cache", "node-cache", "cacheable-node"], "devDependencies": {"@types/eslint": "^9.6.1", "@types/node": "^24.0.7", "@vitest/coverage-v8": "^3.2.4", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.4", "xo": "^1.1.1"}, "dependencies": {"hookified": "^1.10.0", "keyv": "^5.3.4", "cacheable": "^1.10.1"}, "files": ["dist", "license"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run --coverage", "clean": "rimraf ./dist ./coverage ./node_modules"}}